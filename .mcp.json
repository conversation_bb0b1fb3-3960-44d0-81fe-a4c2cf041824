{"mcpServers": {"mcp-feedback-collector": {"type": "stdio", "command": "D:\\Programs\\Python\\Python311\\python.exe", "args": ["-m", "mcp_feedback_collector.server"], "env": {"PYTHONIOENCODING": "utf-8", "MCP_DIALOG_TIMEOUT": "1200", "PYTHONPATH": "D:\\Programs\\Python\\Python311\\Lib\\site-packages"}}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}}, "chrome-mcp-stdio": {"type": "stdio", "command": "npx", "args": ["node", "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"], "env": {"MCP_SERVER_URL": "http://************:12306/mcp", "MCP_HOST": "************", "MCP_PORT": "12306"}}, "nova-memory": {"type": "stdio", "command": "node", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova\\nova-memory-mcp.mjs"], "timeout": 30000, "enabled": true, "env": {"NOVA_MEMORY_PATH": "G:\\BaiduSyncdisk\\nova-memory-mcp\\data", "NOVA_LOG_LEVEL": "info"}, "autoapprove": ["memory", "board", "workflow", "quick", "relationships", "analysis", "project", "settings", "help"]}}}