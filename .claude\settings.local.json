{
  "permissions": {
    "allow": [
      "Bash(mkdir -p test-code test-doc)",
      "Bash(ls -la test-code/ test-doc/)",
      "Bash(find:*)",
      "Bash(ls:*)",
      "Bash(echo $PATH)",
      "Bash(node:*)",
      "Bash(npm:*)",
      "Bash(claude --version)",
      "Bash(if [ -f ~/.nvm/nvm.sh ])",
      "Bash(then echo \"NVM 已安装\")",
      "Bash(else echo \"NVM 未安装\")",
      "Bash(fi)",
      "Bash(cp:*)",
      "Bash(sudo apt remove:*)",
      "Bash(source:*)",
      "Bash(nvm list:*)",
      "Bash(export NVM_DIR=\"$HOME/.nvm\")",
      "Bash([ -s \"$NVM_DIR/nvm.sh\" ])",
      "Bash(. \"$NVM_DIR/nvm.sh\")",
      "Bash(bash:*)",
      "Bash(chmod:*)",
      "Bash(nvm use:*)",
      "Bash(env)",
      "Bash(grep:*)",
      "Bash(nvm:*)",
      "Bash(~/.npm-global/bin/claude:*)",
      "Bash(sudo rm:*)",
      "Bash(rm:*)",
      "Bash(mkdir:*)",
      "Bash(whereis:*)",
      "Bash(echo $SHELL)",
      "Bash(printenv)",
      "Bash(cat:*)",
      "Bash(BACKUP_DIR=~/backup-wsl-cleanup)",
      "Bash(echo:*)",
      "Bash(sudo npm list -g @anthropic-ai/claude-code)",
      "Bash(sudo npm uninstall -g @anthropic-ai/claude-code)",
      "Bash(powershell.exe:*)",
      "Bash(python:*)",
      "Bash(claude mcp:*)",
      "Bash(D:\\Programs\\Python\\Python311\\python.exe --version)",
      "mcp__mcp-feedback-collector__collect_feedback",
      "mcp__mcp-feedback-collector__pick_image",
      "Bash(diff:*)",
      "Bash(D:\\Programs\\Python\\Python311\\python.exe:*)",
      "Bash(true)",
      "Bash(claude /mcp)",
      "WebFetch(domain:docs.anthropic.com)",
      "Bash(claude config list)",
      "Bash(claude:*)",
      "Bash(code:*)",
      "Bash(alias code:*)",
      "Bash('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin\\code' --version)",
      "Bash('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin\\code' --install-extension ms-vscode-remote.remote-wsl)",
      "Bash('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin\\code' --install-extension anthropic.claude-code)",
      "Bash('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin\\code' --list-extensions)",
      "Bash(export:*)",
      "Bash(sqlite3:*)",
      "Bash(./fix-vscode-terminal-path.sh:*)",
      "Bash(hash -r)",
      "Bash(beep)",
      "Bash(ps:*)",
      "Bash(sed:*)",
      "Bash(md5sum:*)",
      "Bash(find C:\\Users -name \"settings.json\" -path \"*/Code/User/*\")",
      "Bash(ls -la \"$HOME/.vscode-server/data/User/\")",
      "Bash(echo \"当前目录：$(pwd)\")",
      "Bash(echo \"1. 当前WSL目录：$(pwd)\")",
      "Bash(echo \"1. 检查当前路径：$(pwd)\")",
      "Bash(ls -la *.code-workspace)",
      "Bash(ls -la .vscode/settings.json)",
      "Bash(mount)",
      "Bash(mv:*)",
      "Bash(cmd.exe /c \"chcp & echo. & echo %LANG% & echo %LC_ALL%\")",
      "mcp__mcp-feedback-collector__collect_feedback",
      "Bash(grep:*)",
      "Bash(source ~/.bashrc)",
      "Bash(find:*)",
      "Bash(rm:*)",
      "Bash(ls:*)",
      "Bash(where.exe:*)",
      "Bash(pnpm:*)",
      "Bash(go:*)",
      "Bash(curl:*)",
      "Bash(PYTHONPATH=\"D:\\Programs\\Python\\Python311\\Lib\\site-packages\" D:\\Programs\\Python\\Python311\\python.exe -m mcp_feedback_collector.server --help)",
      "WebFetch(domain:github.com)",
      "WebFetch(domain:browsermcp.io)",
      "Bash(npx @browser-mcp/mcp-server --help)",
      "mcp__context7__resolve-library-id",
      "mcp__context7__get-library-docs",
      "Bash(check_config_version)",
      "Bash(/tmp/test_claude_config.sh:*)",
      "Bash(dos2unix:*)",
      "Bash(npx:*)",
      "Bash(G:\\z-vscode-claudecode\\config-test\\validate-global-mcp.sh)",
      "Bash(~/.claude/backup-settings.sh:*)",
      "Bash(git clone:*)",
      "Bash(time curl:*)",
      "Bash(/home/<USER>/.claude/backup-settings.sh:*)",
      "Bash(jq . G:\\z-vscode-claudecode\\.claude\\settings.json)",
      "Bash(/usr/bin/python3:*)",
      "Bash(/dev/null)",
      "Bash(pkill:*)",
      "Bash(kill:*)",
      "Bash(timeout:*)",
      "Bash(awk:*)",
      "Bash(git config:*)",
      "Bash(jq:*)",
      "Bash(mcp-chrome-bridge:*)",
      "Bash(MCP_SERVER_URL=\"http://************:12306/mcp\" MCP_HOST=\"************\" MCP_PORT=\"12306\" timeout 10s npx node C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js)",
      "Bash(~/.claude/chrome-mcp-config.sh backup)",
      "Bash(~/.claude/chrome-mcp-config.sh:*)",
      "Bash(~/.claude/chrome-mcp-server.sh:*)",
      "Bash(~/.claude/native-messaging-manager.sh:*)",
      "mcp__chrome-mcp-stdio__chrome_get_interactive_elements",
      "mcp__chrome-mcp-stdio__chrome_screenshot",
      "Bash(./wsl-screenshot.sh)",
      "Bash(NOVA_MEMORY_PATH=\"G:\\BaiduSyncdisk\\nova-memory-mcp\\data\" NOVA_LOG_LEVEL=\"info\" timeout 10s node node_modules/@nova-mcp/mcp-nova/nova-memory-mcp.mjs)",
      "mcp__nova-memory__memory",
      "mcp__nova-memory__settings",
      "mcp__nova-memory__relationships",
      "mcp__nova-memory__workflow",
      "mcp__nova-memory__board",
      "mcp__nova-memory__analysis",
      "mcp__nova-memory__project",
      "mcp__nova-memory__quick"
    ],
    "deny": [],
    "defaultMode": "acceptEdits",
    "additionalDirectories": [
      "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@nova-mcp\\mcp-nova",
      "G:\BaiduSyncdisk\nova-memory-mcp\wsl-version"
    ]
  },
  "enableAllProjectMcpServers": true,
  "enabledMcpjsonServers": [
    "mcp-feedback-collector",
    "context7",
    "chrome-mcp-stdio",
    "nova-memory"
  ]
}
